<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Logo Carousel Layout</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
  <style>
    /* --- Base Styles & Resets --- */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Inter', sans-serif;
      background-color: #f8f9fa;
      color: #333;
      overflow-x: hidden; /* Prevents horizontal scroll from carousel overflow */
    }

    /* --- Layout Styles --- */
    .logo-carousel-section {
      /* Core Requirement: Section takes full viewport height */
      height: 100vh;
      width: 100%;

      /* Layout: Using flexbox to center content vertically and horizontally */
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      /* Visual Aid: Dotted border for the main section container */
      border: 2px dotted #e67e22;
      padding: 2rem;
      position: relative; /* For label positioning */
    }

    .content-wrapper {
      /* Layout: Container for the title and carousel track */
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      max-width: 1200px; /* Constrain width for a clean look */

      /* Visual Aid: Dotted border for the content area */
      border: 2px dotted #3498db;
      padding: 2rem;
      position: relative;
    }

    .carousel-track {
      /* Layout: The "thin line" that will act as the carousel container */
      display: flex;
      align-items: center;
      gap: 4rem; /* Space between logos */
      width: 100%;
      padding: 2rem 0;
      border-top: 1px solid #dee2e6;
      border-bottom: 1px solid #dee2e6;

      /* Visual Aid: Dotted border for the track */
      border-left: 2px dotted #9b59b6;
      border-right: 2px dotted #9b59b6;
      position: relative;
    }

    .logo-frame {
      /* Layout: Rectangular frame for each logo */
      width: 160px;
      height: 80px;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
      flex-shrink: 0; /* Prevents logos from shrinking */

      /* Centering content inside the frame */
      display: flex;
      justify-content: center;
      align-items: center;

      /* Visual Aid: Dotted border for each frame */
      border: 1px dotted #e74c3c;
      position: relative;
    }

    /* --- Content Styles --- */
    .section-title {
      /* Positioning and styling for the section title */
      margin-bottom: 2rem;
      border: 2px dotted #2ecc71;
      padding: 0.5rem 1rem;
      position: relative;
      text-align: center;
      color: #6c757d;
      font-weight: 500;
    }

    /* --- Professional Labels --- */
    .layout-label {
      /* Style for the labels to make them clear but unobtrusive */
      position: absolute;
      top: -12px;
      left: 10px;
      background-color: #f8f9fa;
      color: #555;
      padding: 2px 6px;
      font-size: 12px;
      font-weight: 500;
      border-radius: 4px;
      white-space: nowrap;
    }
  </style>
</head>
<body>

<!-- SECTION: Main container for the entire 100vh component -->
<section class="logo-carousel-section">
  <span class="layout-label">Component: 100vh Section</span>

  <!-- CONTAINER: Centered Content Wrapper -->
  <div class="content-wrapper">
    <span class="layout-label">Container: Content Wrapper</span>

    <!-- ELEMENT: Section Title -->
    <div class="section-title">
      <span class="layout-label">Element: Section Title</span>
      <p>CON LA CONFIANZA DE EMPRESAS LÍDERES</p>
    </div>

    <!-- CONTAINER: Carousel Track -->
    <div class="carousel-track">
      <span class="layout-label">Container: Carousel Track</span>

      <!-- ITEM: Logo Frame -->
      <div class="logo-frame">
        <span class="layout-label">Item: Logo Frame</span>
        <span>Logo 1</span>
      </div>

      <!-- ITEM: Logo Frame -->
      <div class="logo-frame">
        <span class="layout-label">Item: Logo Frame</span>
        <span>Logo 2</span>
      </div>

      <!-- ITEM: Logo Frame -->
      <div class="logo-frame">
        <span class="layout-label">Item: Logo Frame</span>
        <span>Logo 3</span>
      </div>

      <!-- ITEM: Logo Frame -->
      <div class="logo-frame">
        <span class="layout-label">Item: Logo Frame</span>
        <span>Logo 4</span>
      </div>

      <!-- ITEM: Logo Frame -->
      <div class="logo-frame">
        <span class="layout-label">Item: Logo Frame</span>
        <span>Logo 5</span>
      </div>

    </div>

  </div>

</section>

</body>
</html>
