<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Business Showcase Layout</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
  <style>
    /* --- Base Styles & Resets --- */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Inter', sans-serif;
      background-color: #f8f9fa;
      color: #333;
    }

    /* --- Layout Styles --- */
    .business-showcase-section {
      /* Core Requirement: Section takes full viewport height */
      height: 100vh;
      width: 100%;

      /* Layout: Using flexbox to center content vertically and horizontally */
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      /* Visual Aid: Dotted border for the main section container */
      border: 2px dotted #e67e22;
      padding: 2rem;
      position: relative; /* For label positioning */
    }

    .content-wrapper {
      /* Layout: Container for the title and grid */
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      max-width: 900px; /* Constrain grid width for a clean look */

      /* Visual Aid: Dotted border for the content area */
      border: 2px dotted #3498db;
      padding: 2rem;
      position: relative;
    }

    .business-grid {
      /* Layout: The main grid container */
      display: grid;
      /* Creates a 4-column grid. Adjust '4' for more/fewer columns */
      grid-template-columns: repeat(4, 1fr);
      /* Defines a base height for rows, allowing them to grow */
      grid-auto-rows: minmax(120px, auto);
      width: 100%;

      /* Core Requirement: No spacing between items */
      gap: 0;

      /* Visual Aid: Dotted border for the grid */
      border: 2px dotted #9b59b6;
      position: relative;
    }

    .grid-item {
      /* Layout: Base style for each business block */
      background-color: #ffffff;
      flex-shrink: 0;

      /* Centering content inside the frame */
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 1.1rem;
      font-weight: 500;

      /* Visual Aid: Dotted border for each frame */
      border: 1px dotted #e74c3c;
      position: relative;
    }

    /* --- Imbalanced Arrangement Classes --- */
    /* Use these classes to create the varied layout */
    .item-wide { grid-column: span 2; }
    .item-tall { grid-row: span 2; }
    .item-large-square {
      grid-column: span 2;
      grid-row: span 2;
    }

    /* --- Content Styles --- */
    .section-title {
      /* Positioning and styling for the section title */
      margin-bottom: 2rem;
      border: 2px dotted #2ecc71;
      padding: 0.5rem 1rem;
      position: relative;
      text-align: center;
      font-weight: 500;
    }

    /* --- Professional Labels --- */
    .layout-label {
      /* Style for the labels to make them clear but unobtrusive */
      position: absolute;
      top: -12px;
      left: 10px;
      background-color: #f8f9fa;
      color: #555;
      padding: 2px 6px;
      font-size: 12px;
      font-weight: 500;
      border-radius: 4px;
      white-space: nowrap;
    }
  </style>
</head>
<body>

<!-- SECTION: Main container for the entire 100vh component -->
<section class="business-showcase-section">
  <span class="layout-label">Component: 100vh Section</span>

  <!-- CONTAINER: Centered Content Wrapper -->
  <div class="content-wrapper">
    <span class="layout-label">Container: Content Wrapper</span>

    <!-- ELEMENT: Section Title -->
    <div class="section-title">
      <span class="layout-label">Element: Section Title</span>
      <h2>Soluciones para Todo Tipo de Negocio</h2>
    </div>

    <!-- CONTAINER: Business Grid -->
    <div class="business-grid">
      <span class="layout-label">Container: Grid</span>

      <!-- ITEM: Large Square (2x2) -->
      <div class="grid-item item-large-square">
        <span class="layout-label">Item: 2x2</span>
        <span>Restaurantes</span>
      </div>

      <!-- ITEM: Standard (1x1) -->
      <div class="grid-item">
        <span class="layout-label">Item: 1x1</span>
        <span>Clínicas</span>
      </div>

      <!-- ITEM: Standard (1x1) -->
      <div class="grid-item">
        <span class="layout-label">Item: 1x1</span>
        <span>Gimnasios</span>
      </div>

      <!-- ITEM: Tall Rectangle (1x2) -->
      <div class="grid-item item-tall">
        <span class="layout-label">Item: 1x2</span>
        <span>Talleres</span>
      </div>

      <!-- ITEM: Wide Rectangle (2x1) -->
      <div class="grid-item item-wide">
        <span class="layout-label">Item: 2x1</span>
        <span>Tiendas</span>
      </div>

      <!-- ITEM: Standard (1x1) -->
      <div class="grid-item">
        <span class="layout-label">Item: 1x1</span>
        <span>Consultorios</span>
      </div>

    </div>

  </div>

</section>

</body>
</html>
