<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Work Methodology Layout</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* --- Base Styles & Resets --- */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }

        /* --- Layout Styles --- */
        .methodology-section {
            /* Core Requirement: Section takes full viewport height */
            height: 100vh;
            width: 100%;

            /* Layout: Using flexbox to center content vertically and horizontally */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            /* Visual Aid: Dotted border for the main section container */
            border: 2px dotted #e67e22;
            padding: 2rem;
            position: relative; /* For label positioning */
        }

        .content-wrapper {
            /* Layout: Container for the title and steps */
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 800px; /* Constrain width for readability */

            /* Visual Aid: Dotted border for the content area */
            border: 2px dotted #3498db;
            padding: 2rem;
            position: relative;
        }

        .methodology-step {
            /* Layout: Base styles for each methodology row */
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            padding: 1.5rem;
            width: 100%;
            margin-bottom: 1.5rem; /* Space between steps */
            display: flex;
            align-items: center;

            /* Visual Aid: Dotted border for each step */
            border: 2px dotted #9b59b6;
            position: relative;
        }

        .methodology-step:last-child {
            margin-bottom: 0;
        }

        .step-number {
            font-size: 2rem;
            font-weight: 700;
            color: #e67e22;
            margin-right: 1.5rem;
            border: 1px dotted #e74c3c;
            padding: 0.5rem;
            position: relative;
        }

        .step-text {
            border: 1px dotted #e74c3c;
            padding: 0.5rem;
            position: relative;
            flex: 1;
        }

        /* --- Content Styles --- */
        .section-title {
            /* Positioning: Placed towards the top, with margin to space it from the steps */
            margin-bottom: 3rem;
            border: 2px dotted #2ecc71;
            padding: 0.5rem 1rem;
            position: relative;
            text-align: center;
        }

        /* --- Professional Labels --- */
        .layout-label {
            /* Style for the labels to make them clear but unobtrusive */
            position: absolute;
            top: -12px;
            left: 10px;
            background-color: #f8f9fa;
            color: #555;
            padding: 2px 6px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
            white-space: nowrap;
        }
    </style>
</head>
<body>

<!-- SECTION: Main container for the entire 100vh component -->
<section class="methodology-section">
    <span class="layout-label">Component: 100vh Section</span>

    <!-- CONTAINER: Centered Content Wrapper -->
    <div class="content-wrapper">
        <span class="layout-label">Container: Content Wrapper</span>

        <!-- ELEMENT: Section Title -->
        <div class="section-title">
            <span class="layout-label">Element: Section Title</span>
            <h2>Nuestra Metodología de Trabajo</h2>
            <p>Un proceso claro para garantizar tu éxito.</p>
        </div>

        <!-- ITEM: Methodology Step 1 -->
        <div class="methodology-step">
            <span class="layout-label">Item: Step Block</span>
            <div class="step-number"><span class="layout-label">Element: Number</span>1</div>
            <div class="step-text"><span class="layout-label">Element: Text</span><strong>Entender Operaciones y Problemas:</strong> Analizamos a fondo las necesidades de nuestros clientes.</div>
        </div>

        <!-- ITEM: Methodology Step 2 -->
        <div class="methodology-step">
            <span class="layout-label">Item: Step Block</span>
            <div class="step-number"><span class="layout-label">Element: Number</span>2</div>
            <div class="step-text"><span class="layout-label">Element: Text</span><strong>Diseño y Cotización:</strong> Creamos una solución a medida y establecemos el precio del proyecto.</div>
        </div>

        <!-- ITEM: Methodology Step 3 -->
        <div class="methodology-step">
            <span class="layout-label">Item: Step Block</span>
            <div class="step-number"><span class="layout-label">Element: Number</span>3</div>
            <div class="step-text"><span class="layout-label">Element: Text</span><strong>Producción y Lanzamiento:</strong> Desarrollamos e implementamos la solución de forma eficiente.</div>
        </div>

        <!-- ITEM: Methodology Step 4 -->
        <div class="methodology-step">
            <span class="layout-label">Item: Step Block</span>
            <div class="step-number"><span class="layout-label">Element: Number</span>4</div>
            <div class="step-text"><span class="layout-label">Element: Text</span><strong>Soporte y Optimización:</strong> Ofrecemos apoyo constante y mejoramos continuamente.</div>
        </div>

    </div>

</section>

</body>
</html>
