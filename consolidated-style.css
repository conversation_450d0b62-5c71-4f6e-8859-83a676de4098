/* ===================================================================
   ALETHEIA - CONSOLIDATED STYLES
   Modern Software Solutions Website
   Dark Theme with Glassmorphism Effects
   =================================================================== */

/* ===================================================================
   CSS VARIABLES & ROOT STYLES
   =================================================================== */
:root {
    /* Color Palette from style.md specifications */
    --primary-accent: #AEC3C3;      /* Muted light cyan/gray for primary elements */
    --secondary-accent: #5E6666;    /* Darker gray-cyan for secondary elements */
    --background-dark: #333737;     /* Main dark background */
    --dark-green: #333737;         /* Dark gray for alternating sections (same as background-dark) */
    --text-color: #E2E8F0;         /* Light gray for high contrast text */
    --white-bg: #ffffff;            /* White background for contrast sections */
    --black-overlay: rgba(0, 0, 0, 0.2); /* Black with 20% opacity for overlays */
    
    /* Glassmorphism variables */
    --glass-bg: rgba(51, 55, 55, 0.7);
    --glass-blur: blur(12px);
    
    /* Typography */
    --font-primary: 'Inter', 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* ===================================================================
   BASE STYLES & RESETS
   =================================================================== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-primary);
    background-color: var(--background-dark);
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
    font-feature-settings: 'rlig' 1, 'calt' 1;
}

html {
    scroll-behavior: smooth;
}

/* ===================================================================
   HEADER SECTION - Glassmorphism Navigation
   =================================================================== */
.aurora-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 70px; /* Fixed header height for calculations */
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    border-bottom: 1px solid rgba(174, 195, 195, 0.2);
    z-index: 1000;
    transition: all 0.3s ease;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    height: 40px;
    width: auto;
    filter: brightness(1.2) contrast(1.1);
    transition: all 0.3s ease;
}

.logo:hover {
    filter: brightness(1.4) contrast(1.2);
    transform: scale(1.02);
}

.nav-menu {
    display: flex;
    gap: 2.5rem;
    align-items: center;
}

.nav-link {
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-color);
    text-decoration: none;
    padding: 0.5rem 0;
    position: relative;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-accent), var(--secondary-accent));
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover {
    color: var(--primary-accent);
    transform: translateY(-1px);
}

.nav-link:hover::before {
    width: 100%;
}

.nav-highlight {
    background: rgba(94, 102, 102, 0.3);
    padding: 0.6rem 1.2rem;
    border-radius: 12px;
    border: 1px solid rgba(94, 102, 102, 0.5);
    color: var(--text-color);
    font-weight: 600;
    transition: all 0.3s ease;
}

.nav-highlight:hover {
    background: rgba(94, 102, 102, 0.5);
    border-color: var(--primary-accent);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(94, 102, 102, 0.3);
}

.nav-highlight::before {
    display: none;
}

/* ===================================================================
   HERO SECTION - Video Background with Glassmorphism
   =================================================================== */
.hero {
    height: 100vh;
    width: 100%;
    background: var(--background-dark);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: 70px; /* Account for fixed header height */
}

.hero-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    object-fit: cover;
    z-index: 1;
    opacity: 0.4;
    transform: scale(1.1);
}

.hero-video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--black-overlay);
    z-index: 2;
}

.hero-content {
    text-align: center;
    color: var(--text-color);
    padding: 2rem 2rem;
    width: 75%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
    background: transparent;
    backdrop-filter: blur(5px);
}

.hero-main {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
}

.hero-text {
    text-align: center;
    padding: 0.5rem 0;
    max-width: 800px;
}

.hero-title {
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-color);
    margin-bottom: 1.5rem;
    line-height: 1.1;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    text-transform: uppercase;
    letter-spacing: 2px;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-color);
    line-height: 1.4;
    margin-bottom: 2rem;
    font-weight: 300;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.5);
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.9;
}

.hero-cta-buttons {
    display: flex;
    gap: 1.2rem;
    justify-content: center;
    align-items: center;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.cta-button {
    display: inline-block;
    padding: 0.9rem 1.8rem;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    text-align: center;
    min-width: 160px;
    position: relative;
    overflow: hidden;
}

.cta-primary {
    background: #333737;
    color: var(--text-color);
    box-shadow: 0 4px 15px rgba(51, 55, 55, 0.3);
    transition: all 0.3s ease;
}

.cta-primary:hover {
    background: var(--primary-accent);
    color: var(--background-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(174, 195, 195, 0.4);
}

.cta-secondary {
    background: transparent;
    color: var(--text-color);
    border: 2px solid rgba(226, 232, 240, 0.6);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.cta-secondary:hover {
    background: var(--text-color);
    color: var(--background-dark);
    border-color: var(--text-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(226, 232, 240, 0.3);
}



/* ===================================================================
   METHODOLOGY SECTION - Work Process Steps
   =================================================================== */
.methodology-section {
    height: 100vh;
    width: 100%;
    background: var(--dark-green);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem 2rem;
    position: relative;
    box-sizing: border-box;
}

.methodology-section::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60%;
    height: 60%;
    background: radial-gradient(circle, rgba(174, 195, 195, 0.05) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 800px;
    position: relative;
    z-index: 2;
}

.section-title {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-color);
}

.section-title h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 0.8rem;
}

.section-title p {
    font-size: 1.1rem;
    color: var(--text-color);
    opacity: 0.8;
}

.methodology-step {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(174, 195, 195, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    width: 100%;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.methodology-step::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(174, 195, 195, 0.1), transparent);
    transition: left 0.5s ease;
}



.methodology-step:last-child {
    margin-bottom: 0;
}

.step-number {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--primary-accent);
    margin-right: 1.5rem;
    min-width: 55px;
    text-align: center;
}

.step-text {
    flex: 1;
    font-size: 1rem;
    color: var(--text-color);
    line-height: 1.5;
}

.step-text strong {
    color: var(--primary-accent);
    font-weight: 600;
}

/* ===================================================================
   LOGO CAROUSEL SECTION - Client Trust Indicators
   =================================================================== */
.logo-carousel-section {
    height: 100vh;
    width: 100%;
    background: var(--white-bg);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem 2rem;
    position: relative;
    box-sizing: border-box;
}

.logo-carousel-section .section-title {
    margin-bottom: 3rem;
}

.logo-carousel-section .section-title h2 {
    color: var(--background-dark);
}

.logo-carousel-section .section-title p {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--background-dark);
    letter-spacing: 2px;
    text-transform: uppercase;
}

.carousel-track {
    display: flex;
    align-items: center;
    gap: 3rem;
    width: 100%;
    max-width: 1000px;
    padding: 2rem 0;
    border-top: 1px solid rgba(174, 195, 195, 0.2);
    border-bottom: 1px solid rgba(174, 195, 195, 0.2);
    position: relative;
}

.logo-frame {
    width: 160px;
    height: 80px;
    background: rgba(51, 55, 55, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(51, 55, 55, 0.3);
    border-radius: 12px;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
    color: var(--background-dark);
    font-weight: 500;
}



/* ===================================================================
   PRODUCT SHOWCASE SECTION - Service Plans
   =================================================================== */
.product-showcase-section {
    height: 100vh;
    width: 100%;
    background: var(--dark-green);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem 2rem;
    position: relative;
    box-sizing: border-box;
}

.product-showcase-section::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80%;
    height: 80%;
    background: radial-gradient(circle, rgba(174, 195, 195, 0.03) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.product-showcase-section .section-title {
    margin-bottom: 2.5rem;
    position: relative;
    z-index: 2;
}

.carousel-viewport {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    width: 100%;
    max-width: 1200px;
    position: relative;
    z-index: 2;
}

.product-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(174, 195, 195, 0.2);
    border-radius: 20px;
    padding: 2rem 1.5rem;
    width: 300px;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-accent), var(--secondary-accent));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card-main {
    transform: scale(1.05);
    z-index: 10;
    border-color: var(--primary-accent);
    box-shadow: 0 15px 40px rgba(174, 195, 195, 0.2);
}

.card-side {
    transform: scale(0.95);
    opacity: 0.8;
    z-index: 5;
}

.card-title {
    margin-bottom: 1.2rem;
    text-align: center;
}

.card-title h3 {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--primary-accent);
}

.card-description {
    margin-bottom: 1.5rem;
    text-align: center;
}

.card-description p {
    font-size: 0.95rem;
    color: var(--text-color);
    line-height: 1.5;
    opacity: 0.9;
}

.card-list ul {
    list-style: none;
    padding: 0;
}

.card-list li {
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 0.6rem;
    padding-left: 1.5rem;
    position: relative;
    opacity: 0.8;
}

.card-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-accent);
    font-weight: bold;
    font-size: 1.1rem;
}

/* ===================================================================
   BUSINESS SHOWCASE SECTION - Industry Solutions Grid
   =================================================================== */
.business-showcase-section {
    height: 100vh;
    width: 100%;
    background: var(--white-bg);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem 2rem;
    position: relative;
    box-sizing: border-box;
}

.business-showcase-section .section-title h2 {
    color: var(--background-dark);
}

.business-showcase-section .content-wrapper {
    max-width: 900px;
}

.business-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-auto-rows: minmax(120px, auto);
    width: 100%;
    gap: 2px;
    background: var(--background-dark);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(51, 55, 55, 0.3);
}

.grid-item {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--background-dark);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.grid-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(174, 195, 195, 0.1), rgba(94, 102, 102, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}



.item-wide {
    grid-column: span 2;
}

.item-tall {
    grid-row: span 2;
}

.item-large-square {
    grid-column: span 2;
    grid-row: span 2;
    font-size: 1.5rem;
}

/* ===================================================================
   CONTACT SECTION - Social Media & Contact Form
   =================================================================== */
.contact-section {
    height: 100vh;
    width: 100%;
    background: var(--white-bg);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem 2rem;
    position: relative;
    box-sizing: border-box;
}

.contact-section::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 70%;
    height: 70%;
    background: radial-gradient(circle, rgba(174, 195, 195, 0.05) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.contact-section .section-title {
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

.contact-section .section-title h2 {
    color: var(--background-dark);
}

.contact-card {
    display: flex;
    width: 100%;
    max-width: 900px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(51, 55, 55, 0.3);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(51, 55, 55, 0.2);
    position: relative;
    z-index: 2;
}

.social-panel {
    width: 40%;
    background: linear-gradient(135deg, var(--background-dark), rgba(51, 55, 55, 0.9));
    color: var(--text-color);
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
}

.social-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(174, 195, 195, 0.1), transparent);
    pointer-events: none;
}

.social-panel h3 {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--primary-accent);
    margin-bottom: 0.8rem;
    position: relative;
    z-index: 2;
}

.social-panel p {
    font-size: 0.95rem;
    color: var(--text-color);
    margin-bottom: 1.5rem;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

.social-links-container {
    position: relative;
    z-index: 2;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 1rem;
    padding: 0.8rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(174, 195, 195, 0.2);
    border-radius: 8px;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.social-link i {
    font-size: 1.2rem;
    min-width: 20px;
    text-align: center;
}



.form-panel {
    width: 60%;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.8);
}

.form-panel h3 {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--background-dark);
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.2rem;
}

.form-group label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.form-input {
    width: 100%;
    padding: 1rem;
    border: 1px solid rgba(51, 55, 55, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: var(--background-dark);
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.form-input:focus {
    outline: none;
    border-color: var(--background-dark);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 15px rgba(51, 55, 55, 0.3);
}

.form-input::placeholder {
    color: rgba(51, 55, 55, 0.6);
}

.form-button {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--background-dark), rgba(51, 55, 55, 0.8));
    color: var(--white-bg);
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}



/* ===================================================================
   PRICING SECTION - Plans and Pricing Cards
   =================================================================== */
.pricing-section {
    height: 100vh;
    width: 100%;
    background: var(--dark-green);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem 2rem;
    position: relative;
    box-sizing: border-box;
}

.pricing-section::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80%;
    height: 80%;
    background: radial-gradient(circle, rgba(174, 195, 195, 0.04) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.pricing-section .content-wrapper {
    max-width: 1200px;
    position: relative;
    z-index: 2;
}

.pricing-cards {
    display: flex;
    gap: 2rem;
    justify-content: center;
    align-items: stretch;
    flex-wrap: wrap;
}

.pricing-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(174, 195, 195, 0.2);
    border-radius: 20px;
    padding: 2rem 1.5rem;
    width: 300px;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-accent), var(--secondary-accent));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.pricing-card.featured {
    transform: scale(1.05);
    border-color: var(--primary-accent);
    box-shadow: 0 15px 40px rgba(174, 195, 195, 0.2);
}

.pricing-card.featured::before {
    opacity: 1;
}

.pricing-badge {
    position: absolute;
    top: -1px;
    right: 2rem;
    background: linear-gradient(135deg, var(--primary-accent), var(--secondary-accent));
    color: var(--background-dark);
    padding: 0.5rem 1rem;
    border-radius: 0 0 8px 8px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.pricing-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.pricing-header h3 {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--primary-accent);
    margin-bottom: 0.8rem;
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.2rem;
}

.currency {
    font-size: 1.2rem;
    color: var(--text-color);
    opacity: 0.8;
}

.amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-color);
}

.period {
    font-size: 1rem;
    color: var(--text-color);
    opacity: 0.7;
}

.pricing-features {
    flex: 1;
    margin-bottom: 1.5rem;
}

.pricing-features ul {
    list-style: none;
    padding: 0;
}

.pricing-features li {
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 0.8rem;
    padding-left: 1.5rem;
    position: relative;
    opacity: 0.9;
}

.pricing-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-accent);
    font-weight: bold;
    font-size: 1.1rem;
}

.pricing-cta {
    text-align: center;
}

.pricing-button {
    display: inline-block;
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, var(--primary-accent), var(--secondary-accent));
    color: var(--background-dark);
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}



/* ===================================================================
   CUSTOM SCROLLBAR - Matching Design Aesthetic
   =================================================================== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-dark);
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-accent);
    border-radius: 4px;
    transition: background 0.3s ease;
}



/* ===================================================================
   ACCESSIBILITY & FOCUS STATES
   =================================================================== */
.cta-button:focus,
.nav-link:focus,
.form-input:focus,
.form-button:focus {
    outline: 2px solid var(--primary-accent);
    outline-offset: 2px;
}

/* ===================================================================
   ANIMATIONS & MICRO-INTERACTIONS
   =================================================================== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}



/* ===================================================================
   RESPONSIVE DESIGN - Mobile & Tablet Optimization
   =================================================================== */
@media (max-width: 1024px) {
    .carousel-viewport {
        flex-direction: column;
        gap: 1.5rem;
    }

    .product-card {
        width: 100%;
        max-width: 400px;
    }

    .card-main,
    .card-side {
        transform: scale(1);
        opacity: 1;
    }

    .business-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .item-wide,
    .item-large-square {
        grid-column: span 1;
    }

    .item-tall,
    .item-large-square {
        grid-row: span 1;
    }

    .pricing-cards {
        flex-direction: column;
        align-items: center;
    }

    .pricing-card {
        width: 100%;
        max-width: 400px;
    }

    .pricing-card.featured {
        transform: scale(1);
    }
}

@media (max-width: 768px) {
    .aurora-header {
        height: 80px; /* Slightly taller header on mobile */
    }

    .header-container {
        padding: 0.5rem 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .nav-menu {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-link {
        font-size: 0.85rem;
    }

    .nav-highlight {
        padding: 0.4rem 0.8rem;
    }

    .hero {
        padding-top: 80px; /* Account for taller mobile header */
    }

    .hero-content {
        padding: 1rem;
        width: 95%;
    }

    .hero-title {
        font-size: 1.8rem;
        margin-bottom: 0.8rem;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: 1.2rem;
    }

    .hero-cta-buttons {
        flex-direction: column;
        gap: 0.8rem;
        margin-top: 1.2rem;
    }

    .cta-button {
        padding: 0.7rem 1.3rem;
        font-size: 0.95rem;
        min-width: 140px;
    }

    .section-title h2 {
        font-size: 1.8rem;
    }

    .section-title p {
        font-size: 1rem;
    }

    .methodology-step {
        flex-direction: column;
        text-align: center;
        padding: 1.2rem;
        margin-bottom: 0.8rem;
    }

    .step-number {
        margin-right: 0;
        margin-bottom: 0.8rem;
        font-size: 1.8rem;
    }

    .step-text {
        font-size: 0.9rem;
    }

    .carousel-track {
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: center;
    }

    .logo-frame {
        width: 120px;
        height: 60px;
        font-size: 0.9rem;
    }

    .contact-card {
        flex-direction: column;
        margin: 0 1rem;
    }

    .social-panel,
    .form-panel {
        width: 100%;
        padding: 1.5rem;
    }

    .social-panel h3,
    .form-panel h3 {
        font-size: 1.4rem;
    }

    .business-grid {
        grid-template-columns: 1fr;
        gap: 1px;
    }

    .grid-item {
        min-height: 70px;
        font-size: 0.95rem;
    }

    .pricing-card {
        width: 100%;
        padding: 1.5rem 1.2rem;
    }

    .pricing-header h3 {
        font-size: 1.4rem;
    }

    .amount {
        font-size: 2.2rem;
    }

    .product-card {
        width: 100%;
        max-width: 350px;
        padding: 1.5rem 1.2rem;
    }

    .card-title h3 {
        font-size: 1.4rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.6rem;
    }

    .hero-description {
        font-size: 0.95rem;
    }

    .section-title h2 {
        font-size: 1.6rem;
    }

    .section-title p {
        font-size: 0.95rem;
    }

    .product-card {
        padding: 1.3rem 1rem;
    }

    .methodology-step {
        padding: 1rem;
    }

    .step-number {
        font-size: 1.6rem;
    }

    .step-text {
        font-size: 0.85rem;
    }

    .contact-card {
        margin: 0 0.5rem;
    }

    .social-panel,
    .form-panel {
        padding: 1.2rem;
    }

    .social-panel h3,
    .form-panel h3 {
        font-size: 1.3rem;
    }

    .logo-frame {
        width: 100px;
        height: 50px;
        font-size: 0.8rem;
    }

    .pricing-card {
        padding: 1.3rem 1rem;
    }

    .amount {
        font-size: 2rem;
    }
}

/* FOOTER */
.footer {
    position: relative;
    width: 100%;
    height: auto;
    overflow: hidden;
    display: flex;
    justify-content: center; 
    align-items: flex-start; 
    flex-direction: column;
    background-color: #333737; 
    padding: 40px;
    color: #AEC3C3;
  }
  
  .footer-contenido {
    position: relative;
    display: flex;
    justify-content: center;
    width: 75%;
    flex-direction: row;
    gap: 60px;
    margin-left: auto;
    margin-right: auto;
  
  }
  
  .footer-col {
    width: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }
  
  .footer-col h3 {
    margin-bottom: 10px;
  }
  
  .footer-col ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .footer-col ul li {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .footer-col ul li i {
    min-width: 18px;
  }
  
  .footer-col ul .direccion {
    align-items: flex-start;
  }
  .footer-col ul .direccion i {
    padding-top: 3px;
  }
  
  .footer-col ul li a {
    text-decoration: none;
    color: #AEC3C3;
    font-size: 1em;
  }
  
  
  
  .copy {
    position: relative;
    width: 100%;
    justify-content: center;
    align-items: flex-start;
    flex-direction: column;
    margin-left: auto;
    margin-right: auto;
  }
  
  .copy hr {
    border: none;
    height: 1px;
    background-color: #AEC3C3;
    margin-top: 30px;
    margin-bottom: 15px;
    width: 75%;
    margin-left: auto;
    margin-right: auto;
  }
  
  .copy p {
   text-align: center;
  }
  
  @media (max-width: 768px) {
    .footer-contenido {
      flex-direction: column;
    }
  }
  
  .business-images-layout {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 24px;
    margin: 32px 0;
}

.business-image {
    width: 220px;
    height: 160px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
    background: #fff;
    transition: transform 0.2s;
}

.business-image:hover {
    transform: scale(1.04);
}

.business-image-container {
    position: relative;
    display: inline-block;
}

.business-image-label {
    position: absolute;
    left: 50%;
    bottom: 12px;
    transform: translateX(-50%);
    color: #fff;
    background: rgba(0,0,0,0.55);
    padding: 6px 18px;
    border-radius: 16px;
    font-size: 1.05rem;
    font-weight: 600;
    text-shadow: 0 2px 8px rgba(0,0,0,0.7);
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
    backdrop-filter: blur(2.5px);
    pointer-events: none;
    letter-spacing: 0.5px;
}
  
  