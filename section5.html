<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contact Section Layout</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
  <style>
    /* --- Base Styles & Resets --- */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Inter', sans-serif;
      background-color: #f8f9fa;
      color: #333;
    }

    /* --- Layout Styles --- */
    .contact-section {
      /* Core Requirement: Section takes full viewport height */
      height: 100vh;
      width: 100%;

      /* Layout: Using flexbox to center content vertically and horizontally */
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      /* Visual Aid: Dotted border for the main section container */
      border: 2px dotted #e67e22;
      padding: 2rem;
      position: relative; /* For label positioning */
    }

    .contact-card {
      /* Layout: The main card container */
      display: flex;
      width: 100%;
      max-width: 900px; /* Constrain card width */
      background-color: #ffffff;
      border-radius: 16px;
      box-shadow: 0 8px 30px rgba(0,0,0,0.1);
      overflow: hidden; /* Ensures child borders don't poke out */

      /* Visual Aid: Dotted border for the card */
      border: 2px dotted #3498db;
      position: relative;
    }

    .social-panel {
      /* Layout: Left side of the card for social media */
      width: 40%;
      background-color: #343a40;
      color: #fff;
      padding: 3rem;
      display: flex;
      flex-direction: column;
      justify-content: center;

      /* Visual Aid: Dotted border for the social panel */
      border: 2px dotted #9b59b6;
      position: relative;
    }

    .form-panel {
      /* Layout: Right side of the card for the form */
      width: 60%;
      padding: 3rem;

      /* Visual Aid: Dotted border for the form panel */
      border: 2px dotted #1abc9c;
      position: relative;
    }

    /* --- Content Styles --- */
    .section-title {
      margin-bottom: 2rem;
      border: 2px dotted #2ecc71;
      padding: 0.5rem 1rem;
      position: relative;
      text-align: center;
    }

    .social-links-container {
      margin-top: 1.5rem;
      border: 1px dotted #e74c3c;
      padding: 1rem;
      position: relative;
    }

    .social-link {
      display: block;
      margin-bottom: 1rem;
      border: 1px dotted #e74c3c;
      padding: 0.5rem;
      position: relative;
    }

    .form-group {
      margin-bottom: 1.5rem;
      border: 1px dotted #e74c3c;
      padding: 0.5rem;
      position: relative;
    }

    .form-input {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #ccc;
      border-radius: 8px;
    }

    .form-button {
      width: 100%;
      padding: 0.75rem;
      border: none;
      border-radius: 8px;
      background-color: #e67e22;
      color: white;
      font-weight: 700;
      cursor: pointer;
    }

    /* --- Professional Labels --- */
    .layout-label {
      position: absolute;
      top: -12px;
      left: 10px;
      background-color: #f8f9fa;
      color: #555;
      padding: 2px 6px;
      font-size: 12px;
      font-weight: 500;
      border-radius: 4px;
      white-space: nowrap;
    }

    .layout-label.dark-bg {
      background-color: #343a40;
      color: #fff;
    }
  </style>
</head>
<body>

<!-- SECTION: Main container for the entire 100vh component -->
<section class="contact-section">
  <span class="layout-label">Component: 100vh Section</span>

  <!-- ELEMENT: Section Title -->
  <div class="section-title">
    <span class="layout-label">Element: Section Title</span>
    <h2>Hablemos</h2>
  </div>

  <!-- CONTAINER: Contact Card -->
  <div class="contact-card">
    <span class="layout-label">Container: Contact Card</span>

    <!-- SUB-CONTAINER: Social Media Panel -->
    <div class="social-panel">
      <span class="layout-label dark-bg">Container: Social Panel</span>
      <h3>Nuestras Redes</h3>
      <p>Encuéntranos y síguenos para no perderte ninguna novedad.</p>
      <div class="social-links-container">
        <span class="layout-label dark-bg">Element: Social Links</span>
        <div class="social-link"><span>Instagram</span></div>
        <div class="social-link"><span>Facebook</span></div>
        <div class="social-link"><span>LinkedIn</span></div>
      </div>
    </div>

    <!-- SUB-CONTAINER: Form Panel -->
    <div class="form-panel">
      <span class="layout-label">Container: Form Panel</span>
      <h3>Envíanos un Mensaje</h3>
      <form>
        <div class="form-group">
          <span class="layout-label">Element: Form Group</span>
          <label>Nombre</label>
          <input type="text" class="form-input">
        </div>
        <div class="form-group">
          <span class="layout-label">Element: Form Group</span>
          <label>Email</label>
          <input type="email" class="form-input">
        </div>
        <div class="form-group">
          <span class="layout-label">Element: Form Group</span>
          <label>Mensaje</label>
          <textarea class="form-input" rows="4"></textarea>
        </div>
        <div class="form-group">
          <span class="layout-label">Element: Button</span>
          <button type="submit" class="form-button">Enviar</button>
        </div>
      </form>
    </div>

  </div>

</section>

</body>
</html>
